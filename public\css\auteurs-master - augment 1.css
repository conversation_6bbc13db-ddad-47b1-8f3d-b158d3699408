/* ===== AUTEURS.SPACE MASTER STYLESHEET ===== */
/* <PERSON>oughly refactored for better organization, maintainability, and semantic naming */
/* Design remains exactly as before - only code structure improved */

/*
 * GLOBAL CARD BORDER CONTROL:
 * To show borders: Set --card-border-visibility to "1px solid #eee"
 * To hide borders: Set --card-border-visibility to "none"
 */

/*
 * TABLE OF CONTENTS:
 * 1. CSS Variables & Design Tokens
 * 2. Base Styles & Reset
 * 3. Layout System
 * 4. Typography System
 * 5. Navigation Components
 * 6. Form Elements & Buttons
 * 7. Card Components
 * 8. Book Display Components
 * 9. Page-Specific Styles
 *    9.1. Vault Page
 *    9.2. Home Page
 *    9.3. Reader Pages
 *    9.4. Author Pages
 *    9.5. Review Pages
 *    9.6. Activity Sections
 * 10. Utility Classes
 * 11. Responsive Design
 * 12. Feedback System
 */

/* ======================
   1. CSS VARIABLES & DESIGN TOKENS
   ====================== */
:root {
    /* Color System - Dynamically overridden by <PERSON><PERSON>elper */
    /* --primary-color: #333; */
    /* --accent-color: #666; */
    --app-background-color: white;
    --text-primary: #333;
    --text-secondary: #666;
    --border-light: #eee;
    --border-medium: #ddd;
    --border-dark: #aaa;

    /* Typography Scale */
    --font-size-xs: 11px;
    --font-size-sm: 12px;
    --font-size-base: 14px;
    --font-size-md: 16px;
    --font-size-lg: 18px;
    --font-size-xl: 30px;
    --font-size-quote: 0.9rem;

    --font-weight-light: 300;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-bold: 600;

    --line-height-tight: 1.2;
    --line-height-normal: 1.4;
    --line-height-relaxed: 1.6;

    /* Spacing System */
    --space-xs: 5px;
    --space-sm: 10px;
    --space-md: 15px;
    --space-lg: 20px;
    --space-xl: 30px;
    --space-2xl: 40px;

    /* Component Tokens */
    --border-radius-sm: 3px;
    --border-radius-md: 5px;
    --border-radius-lg: 8px;
    --border-radius-xl: 12px;

    /* Card System */
    --card-padding: var(--space-md);
    --card-margin: var(--space-sm);
    --card-background: #fff;
    --card-border-radius: var(--border-radius-md);
    --card-border-width: 1px;
    --card-border-style: solid;
    --card-border-color: var(--border-light);
    --card-border-visibility: none; /* Global border control */
    --card-line-height: var(--line-height-tight);

    /* Navigation System */
    --nav-text-color: #000;
    --nav-hover-color: var(--accent-color);
    --nav-active-color: var(--accent-color);
    --nav-font-size: var(--font-size-base);
    --nav-font-size-mobile: 18px;
    --nav-gap: var(--space-sm);
    --nav-transition: color 0.3s ease;

    /* Quote System */
    --quote-font-size: var(--font-size-quote);
    --quote-color: #000;
    --quote-max-width: 600px;

    /* Filter System */
    --filter-label-font-size: var(--font-size-xs);
    --filter-gap: var(--space-sm);
    --filter-margin-bottom: var(--space-xs);

    /* Animation System */
    --transition-fast: 0.2s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
}

/* ======================
   2. BASE STYLES & RESET
   ====================== */

/* Document Reset */
html, body {
    margin: 0;
    padding: 0;
    overflow-x: hidden;
    background-color: var(--app-background-color) !important;
}

/* Base Body Styles */
body {
    margin-top: var(--space-sm);
    line-height: var(--line-height-relaxed);
    color: var(--text-primary);
    /* font-family: "Segoe UI", Arial, sans-serif; */ /* Commented in original */
}

/* Link System */
.link-base,
a,
a:visited {
    color: #000;
    text-decoration: none;
    transition: var(--transition-normal);
}

.link-base:hover,
a:hover {
    color: var(--accent-color);
    text-decoration: none;
}

/* Icon System - Force black icons globally */
.icon-system,
html body [class^="ri-"],
html body [class*=" ri-"],
html body .icon,
html body i,
html body svg,
html body .fa,
html body .fas,
html body .far,
html body .fab,
html body button [class^="ri-"],
html body button [class*=" ri-"],
html body a [class^="ri-"],
html body a [class*=" ri-"],
html body .btn [class^="ri-"],
html body .btn [class*=" ri-"],
html body *[class^="ri-"],
html body *[class*=" ri-"],
html body svg *,
html body svg path,
html body svg rect,
html body svg circle,
html body svg line,
html body svg polygon,
html body *:hover [class^="ri-"],
html body *:hover [class*=" ri-"],
html body *:hover .icon,
html body *:hover i,
html body *:hover svg,
html body *:hover .fa,
html body *:hover .fas,
html body *:hover .far,
html body *:hover .fab {
    color: black !important;
    fill: black !important;
    stroke: black !important;
}

/* Rating System - Hide star icons but keep numbers */
.rating-star-hidden .ri-star-fill {
    display: none !important;
}

/* ======================
   3. LAYOUT SYSTEM
   ====================== */

/* Main Container */
.app-container,
.container {
    max-width: 1000px !important;
    margin-left: auto !important;
    margin-right: auto !important;
    padding-left: var(--space-xs) !important;
    padding-right: var(--space-xs) !important;
    width: 100% !important;
}

/* Custom Grid System */
.grid-col-1-5,
.col-md-1-5 {
    position: relative;
    width: 100%;
    padding-right: 0;
    padding-left: 0;
}

@media (min-width: 768px) {
    .grid-col-1-5,
    .col-md-1-5 {
        -ms-flex: 0 0 12.5%;
        flex: 0 0 12.5%;
        max-width: 12.5%;
    }
}

/* Header Layout */
.header-desktop,
.deskHEader {
    margin-top: 0;
    padding-top: 0;
}

.header-mobile,
.mobmenu {
    margin-top: 0;
    padding-top: 0;
    display: none; /* Hidden by default */
}

/* Page Layout */
.page-content,
.page-content-container {
    margin-top: 0;
    padding-top: 0;
    background-color: var(--app-background-color) !important;
}

/* Image System */
.image-fluid,
img.img-fluid {
    display: block !important;
    /* vertical-align removed due to display: block */
}

/* ======================
   4. TYPOGRAPHY SYSTEM
   ====================== */

/* Primary Headings */
.heading-primary,
.heading {
    font-size: var(--font-size-xl) !important;
    font-weight: var(--font-weight-light) !important;
    color: var(--primary-color) !important;
    margin-bottom: var(--space-lg) !important;
}

/* Secondary Headings */
.heading-secondary,
.page-subtitle {
    color: var(--primary-color) !important;
    font-weight: var(--font-weight-light) !important;
    font-size: var(--font-size-xl) !important;
}

/* Section Headings with Decorative Lines */
.section-heading-decorated,
h3.section-heading,
h3.page_section_heading,
h3.reader-section-heading,
h3.author-section-heading,
h3.vault-section-heading,
h3.review-section-heading,
h3.section-title,
h3.activity-heading,
.section-heading,
.page_section_heading,
.reader-section-heading,
.author-section-heading,
.vault-section-heading,
.review-section-heading,
.section-title,
.activity-heading {
    /* Typography */
    font-size: 1.3rem !important;
    font-weight: var(--font-weight-light) !important;
    color: var(--accent-color) !important;
    margin: var(--space-xl) 0 var(--space-2xl) !important;
    padding: 0 !important;

    /* Layout */
    display: flex !important;
    width: 100% !important;
    align-items: baseline !important;
    justify-content: center !important;
    text-align: center !important;
    position: relative !important;

    /* Reset */
    border: none !important;
    box-shadow: none !important;
    background: none !important;
}

/* Decorative Lines for Section Headings */
.section-heading-decorated::before,
.section-heading-decorated::after,
h3.section-heading::before,
h3.page_section_heading::before,
h3.reader-section-heading::before,
h3.author-section-heading::before,
h3.vault-section-heading::before,
h3.review-section-heading::before,
h3.section-title::before,
h3.activity-heading::before,
.section-heading::before,
.page_section_heading::before,
.reader-section_heading::before,
.author-section_heading::before,
.vault-section_heading *::before,
.review-section_heading *::before,
.section-title *::before,
.activity-heading *::before,
h3.section-heading::after,
h3.page_section_heading::after,
h3.reader-section-heading::after,
h3.author-section-heading::after,
h3.vault-section-heading::after,
h3.review-section-heading::after,
h3.section-title::after,
h3.activity-heading::after,
.section-heading::after,
.page_section_heading::after,
.reader-section_heading::after,
.author-section_heading::after,
.vault-section_heading *::after,
.review-section_heading *::after,
.section-title *::after,
.activity-heading *::after {
    content: "" !important;
    display: block !important;
    flex-grow: 1 !important;
    height: 1px !important;
    background-color: #f5f5f5 !important;
    margin: 0 var(--space-lg) !important;
    align-self: baseline !important;
}

/* Hide Child Pseudo-elements */
.section-heading-decorated *::before,
.section-heading-decorated *::after,
h3.section-heading *::before,
h3.page_section_heading *::before,
h3.reader-section_heading *::before,
h3.author-section_heading *::before,
h3.vault-section_heading *::before,
h3.review-section_heading *::before,
h3.section-title *::before,
h3.activity-heading *::before,
.section-heading *::before,
.page_section_heading *::before,
.reader-section_heading *::before,
.author-section_heading *::before,
.vault-section_heading *::before,
.review-section_heading *::before,
h3.section-heading *::after,
h3.page_section_heading *::after,
h3.reader-section_heading *::after,
h3.author-section_heading *::after,
h3.vault-section_heading *::after,
h3.review-section_heading *::after,
h3.section-title *::after,
h3.activity-heading *::after,
.section-heading *::after,
.page_section_heading *::after,
.reader-section_heading *::after,
.author-section_heading *::after,
.vault-section_heading *::after,
.review-section_heading *::after,
.section-title *::after,
.activity-heading *::after {
    display: none !important;
    content: none !important;
}

/* Quote System - moved from later in file */
.quote-system,
.literary-quote,
.vault-literary-quote,
.reader-literary-quote,
.author-literary-quote {
    font-size: var(--quote-font-size);
    color: var(--quote-color) !important;
    margin-top: var(--space-sm) !important;
    margin-bottom: var(--space-lg);
    max-width: var(--quote-max-width);
    margin-left: auto;
    margin-right: auto;
    text-align: center;
    font-weight: var(--font-weight-light);
}

.quote-text {
    font-style: normal;
    font-size: var(--quote-font-size);
}

.quote-separator {
    font-style: normal;
    font-size: var(--quote-font-size);
}

.quote-source {
    font-style: italic;
    font-size: calc(var(--quote-font-size) - 0.1rem);
}

.quote-container {
    padding: 2rem 0;
    background: var(--app-background-color);
}

/* ======================
   5. NAVIGATION COMPONENTS
   ====================== */
/* Navigation Container */
.nav-container {
    display: flex;
    align-items: center;
    height: 100%;
}

.nav-container ul {
    display: flex;
    list-style-type: none;
    gap: var(--space-lg);
    height: 100%;
    align-items: center;
    justify-content: center;
    padding: 0;
    margin: 0;
}

/* Page Submenu System */
.page-submenu {
    padding: var(--space-xs) 0;
    margin-bottom: var(--space-md);
}

.page-submenu .nav {
    display: flex;
    list-style-type: none;
    gap: 2.5px; /* Half spacing for tighter layout */
    height: 100%;
    align-items: center;
    justify-content: center;
    padding: 0;
    margin: 0;
}

.page-submenu .nav-item {
    display: flex;
    align-items: center;
}

.page-submenu .nav-link {
    color: var(--nav-text-color);
    font-size: var(--nav-font-size);
    font-weight: var(--font-weight-light);
    text-transform: capitalize;
    transition: var(--nav-transition);
    padding: 0;
}

.page-submenu .nav-link:hover {
    color: var(--accent-color);
}

.page-submenu .nav-link.active {
    color: var(--accent-color);
    font-weight: var(--font-weight-light);
}

/* Navigation Radio Button System */
.nav-radio-group {
    display: flex;
    gap: var(--space-lg);
    align-items: center;
}

.nav-radio-group label {
    display: flex;
    gap: var(--space-sm);
    align-items: center;
    cursor: pointer;
    transition: var(--transition-normal);
}

.nav-radio-group label:hover {
    color: var(--accent-color) !important;
    cursor: pointer;
}

.nav-radio-group label:hover span {
    color: var(--accent-color) !important;
}

.nav-radio-input,
.nav-radio-group input[type="radio"] {
    margin: 0 !important;
    cursor: pointer !important;
    appearance: none !important;
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
    width: 12px !important;
    height: 12px !important;
    border: 1px solid var(--border-dark) !important;
    border-radius: 50% !important;
    background-color: var(--app-background-color) !important;
    position: relative !important;
    outline: none !important;
}

.nav-radio-input:checked,
.nav-radio-group input[type="radio"]:checked {
    background-color: var(--app-background-color) !important;
    border-color: var(--accent-color) !important;
}

.nav-radio-input:checked::after,
.nav-radio-group input[type="radio"]:checked::after {
    content: '' !important;
    position: absolute !important;
    left: 2px !important;
    top: 2px !important;
    width: 6px !important;
    height: 6px !important;
    border-radius: 50% !important;
    background-color: var(--accent-color) !important;
}

.nav-radio-label,
.nav-radio-group span {
    color: var(--nav-text-color);
    font-size: var(--nav-font-size) !important;
    font-weight: var(--font-weight-light);
    text-transform: capitalize;
    transition: var(--nav-transition);
}

.nav-container .nav-radio-group span {
    font-size: var(--nav-font-size) !important;
}

.nav-radio-input:checked + span,
.nav-radio-group input[type="radio"]:checked + span {
    color: var(--nav-active-color);
    font-weight: var(--font-weight-light);
}

/* User System */
.user-name,
.header-user-name,
.header-user-name a,
#userTrigger a {
    font-size: var(--font-size-base) !important;
    font-weight: var(--font-weight-light) !important;
}

/* User Dropdown */
.user-dropdown,
#userDropdown {
    display: none;
    position: absolute;
    top: 100%;
    right: 0;
    background: var(--app-background-color);
    border: 1px solid var(--border-medium);
    border-radius: var(--border-radius-sm);
    box-shadow: none !important;
    z-index: 9999;
    min-width: 150px;
    padding: 8px 0;
    margin-top: var(--space-xs);
    opacity: 0;
    visibility: hidden;
    transition: var(--transition-fast);
    transform-origin: top right;
    font-size: var(--font-size-sm) !important;
}

.user-dropdown.active,
#userDropdown.active {
    display: block;
    opacity: 1;
    visibility: visible;
    height: auto;
    width: auto;
}

.user-dropdown ul,
#userDropdown ul {
    list-style: none;
    padding: 0;
    margin: 0;
    width: 100%;
    display: block;
}

.user-dropdown-item,
#userDropdown ul li {
    padding: 8px 15px;
    width: 100%;
    box-sizing: border-box;
    display: block;
    height: auto;
}

.user-dropdown-item:hover,
#userDropdown ul li:hover {
    background-color: #f5f5f5;
}

.user-dropdown-link,
#userDropdown ul li a {
    color: var(--text-primary);
    text-decoration: none;
    display: block;
    width: 100%;
    white-space: nowrap;
    overflow: visible;
    text-overflow: clip;
    padding: 0;
    height: auto;
    line-height: normal;
    font-size: var(--font-size-sm) !important;
}

.user-dropdown-link:hover,
#userDropdown ul li a:hover {
    color: var(--accent-color);
}

/* Wallet Balance */
.wallet-balance,
.header-wallet-balance {
    font-size: var(--font-size-base);
    display: flex;
    align-items: center;
    gap: var(--space-xs);
    margin: 0;
}

.wallet-balance i,
.header-wallet-balance i {
    color: red;
}

/* Notification System */
.notification-trigger,
.header-notification,
.header_notification {
    position: relative;
    cursor: pointer;
}

.notification-icon,
.header-notification-icon,
#showNotification {
    font-size: var(--font-size-sm);
    cursor: pointer;
}

.notification-badge {
    position: absolute;
    top: -6px;
    right: -6px;
    background-color: red;
    color: white;
    font-size: 10px;
    min-width: 16px;
    height: 16px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: var(--font-weight-normal);
    padding: 1px;
}

.notification-dropdown,
.notification-container,
.notification_container {
    display: none;
    position: absolute;
    top: 100%;
    right: 0;
    background: var(--app-background-color);
    border: 1px solid var(--border-medium);
    border-radius: var(--border-radius-sm);
    box-shadow: none !important;
    width: 300px;
    max-height: 400px;
    overflow-y: auto;
    z-index: 1000;
}

.notification-dropdown.active,
.notification-container.active,
.notification_container.active {
    display: block;
}

.notification-container table,
.notification_container table {
    font-size: var(--font-size-xs);
}

/* ======================
   6. FORM ELEMENTS & BUTTONS
   ====================== */

/* Primary Button System */
.btn-primary-base,
.btn-primary {
    background-color: var(--accent-color) !important;
    border-color: var(--accent-color) !important;
    color: white !important;
    border-radius: 0 !important;
    padding: 0.5rem 1rem !important;
    transition: var(--transition-normal);
    font-weight: var(--font-weight-light) !important;
}

.btn-primary-hover,
.btn-primary:hover,
.btn.btn-primary:hover,
.btn.btn-sm.btn-primary:hover,
button.btn-primary:hover,
button.btn.btn-primary:hover,
button.btn.btn-sm.btn-primary:hover {
    background-color: var(--primary-color) !important;
    border-color: var(--primary-color) !important;
    color: var(--accent-color) !important;
}

/* Text Link Button System */
.btn-text-link-base,
.btn-text-link {
    background: none !important;
    border: none !important;
    color: var(--accent-color) !important;
    text-decoration: underline !important;
    padding: 0 !important;
    font-weight: var(--font-weight-light) !important;
    font-size: 1.0rem !important;
    transition: var(--transition-normal);
    margin-right: 1.5rem !important;
    display: inline-block !important;
}

.btn-text-link-hover,
.btn-text-link:hover {
    background: none !important;
    color: black !important;
    text-decoration: underline !important;
}

/* Global style for book field titles */
.CreateBookFields {
    font-weight: 300;
    margin-bottom: 5px;
    display: block;
    font-size: 18px;
    color: var(--primary-color);

}

.BookFieldsExplain {
    font-weight: 300;
    margin-bottom: 5px;
    display: block;
    font-size: 14px;
    color: 666;
}

/* User Status Management Styles */
.status-toggle {
    cursor: pointer;
}

.status-toggle:disabled {
    cursor: not-allowed;
}

.badge.bg-warning {
    background-color: #ffc107 !important;
}

.badge.bg-success {
    background-color: #198754 !important;
}

.form-check-label {
    cursor: pointer;
    font-weight: 500;
}

.table-responsive {
    border-radius: 8px;
    overflow: hidden;
}

.table th {
    background-color: #f8f9fa;
    font-weight: 600;
    border-bottom: 2px solid #dee2e6;
}

.table td {
    vertical-align: middle;
}

/* Vault blocked message styles */
.vault-flash-message {
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
    font-weight: 500;
}

.vault-flash-message.alert-warning {
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
    color: #856404;
}

/* User Management Pagination Styles */
.pagination-info {
    font-size: 14px;
    color: #6c757d;
}

.pagination-controls .pagination {
    margin-bottom: 0;
}

.pagination-controls .page-link {
    color: #007bff;
    border-color: #dee2e6;
}

.pagination-controls .page-item.active .page-link {
    background-color: #007bff;
    border-color: #007bff;
}

/* Inactive user styling */
.text-danger {
    color: #dc3545 !important;
}

.text-success {
    color: #198754 !important;
}

/* Form check styling for inactive users toggle */
.form-check-input:checked {
    background-color: #007bff;
    border-color: #007bff;
}

.form-check-label {
    font-weight: 500;
    margin-bottom: 0;
}

/* Show More/Less functionality for Reviews History, Friendly Notes, and Activity */
.reviews-list-container:not(.expanded) .review-history-item:nth-child(n+5) {
    display: none;
}

.notes-list-container:not(.expanded) .note-card:nth-child(n+3) {
    display: none;
}

.activity-tbody:not(.expanded) .activity-row:nth-child(n+11) {
    display: none;
}

/* Style for field descriptions */
.field-description {
    font-size: 12px;
    color: #666;
    display: block;
    margin-top: 2px;
}

/* Quiz Guide Styles */
.quiz-guide-content {
    max-width: 100%;
    line-height: 1.6;
}

.quiz-quote {
    background-color: #f8f9fa;
    border-left: 4px solid #007bff;
    padding: 15px 20px;
    margin: 15px 0;
    font-style: italic;
}

.quiz-quote p {
    margin: 0;
    color: #495057;
}

.quiz-list {
    list-style: none;
    padding-left: 0;
    margin: 10px 0;
}

.quiz-list li {
    padding: 5px 0;
    font-size: 14px;
    color: #666;
}

.quiz-tip {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    margin: 15px 0;
    border: 1px solid #e9ecef;
}

.quiz-tip-title {
    color: #007bff;
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 10px;
}

.quiz-avoid {
    color: #dc3545;
    font-weight: 500;
    font-size: 14px;
}

.quiz-example {
    background-color: #fff;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 12px;
    margin: 10px 0;
}

.quiz-question {
    font-weight: 500;
    color: #495057;
    margin-bottom: 8px;
}

.quiz-answer {
    color: #28a745;
    font-weight: 500;
    font-size: 14px;
}

.quiz-options {
    font-size: 14px;
    color: #666;
    line-height: 1.4;
}

.quiz-example-section {
    background-color: #e8f4fd;
    border-radius: 8px;
    padding: 20px;
    margin: 15px 0;
}

.quiz-example-list {
    padding-left: 20px;
}

.quiz-example-list li {
    margin-bottom: 15px;
    font-size: 14px;
    color: #666;
}

.quiz-note {
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 6px;
    padding: 10px;
    margin-top: 15px;
    color: #856404;
}

.quiz-humor-section {
    background-color: #f0f8ff;
    border-radius: 8px;
    padding: 20px;
    margin: 15px 0;
}

.quiz-humor-example {
    background-color: #fff;
    border-radius: 6px;
    padding: 15px;
    margin: 10px 0;
    border: 1px solid #b3d9ff;
}

.quiz-humor-note {
    color: #6c757d;
    font-style: italic;
    font-size: 13px;
    margin-top: 5px;
}

.knowledge-base-link {
    color: #007bff;
    text-decoration: underline;
    font-weight: 500;
}

.knowledge-base-link:hover {
    color: #0056b3;
    text-decoration: underline;
}

/* Character counter positioning */
.character-counter {
    font-size: 12px;
    text-align: right;
    display: block;
    margin-bottom: 5px;
}

.character-counter.invalid {
    color: #dc3545;
}

.character-counter.valid {
    color: #198754;
}

/* Form field validation styling */
.form-control.is-valid {
    border-color: #198754;
}

.form-control.is-invalid {
    border-color: #dc3545;
    font-size: 12px;
}

/* Style for readonly input fields */
input[readonly] {
    background-color: #f8f9fa !important; /* Light gray background */
    color: #6c757d !important; /* Darker gray text */
    cursor: not-allowed !important; /* Indicate not editable */
}

/* Position error messages on the left */
.error-message {
    display: block;
    text-align: left;
    margin-top: 5px;
    font-size: 12px;
}

/* Style for quiz option labels */
.quiz-option-label {
    font-weight: 400;
    color: var(--primary-color);
    margin-left: 5px;
    display: inline-block;
}

/* Review quiz radio buttons - Consolidated common styles */
.review-radio-group,
.rating-div.review-radio-group, /* Included rating-div here */
.turnaround-radio-group {
     /* display handled by specific classes */
     /* gap handled by specific classes */
     /* align-items handled by specific classes */
     /* flex-wrap handled by specific classes */
}

.review-radio-group label,
.rating-div.review-radio-group label,
.turnaround-radio-group label {
    display: flex !important;
    gap: var(--base-spacing) !important;
    align-items: center !important;
    cursor: pointer !important;
    margin-bottom: 5px !important;
}

.review-radio-group input[type="radio"],
.rating-div.review-radio-group input[type="radio"],
.turnaround-radio-group input[type="radio"] {
    margin: 0 !important;
    cursor: pointer !important;
    appearance: none !important;
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
    width: 12px !important;
    height: 12px !important;
    border: 1px solid #aaa !important;
    border-radius: 50% !important;
    background-color: #fff !important;
    position: relative !important;
    outline: none !important;
}

.review-radio-group input[type="radio"]:checked,
.rating-div.review-radio-group input[type="radio"]:checked,
.turnaround-radio-group input[type="radio"]:checked {
    background-color: #fff !important;
    border-color: var(--accent-color) !important;
}

.review-radio-group input[type="radio"]:checked::after,
.rating-div.review-radio-group input[type="radio"]:checked::after,
.turnaround-radio-group input[type="radio"]:checked::after {
    content: '' !important;
    position: absolute !important;
    left: 2px !important;
    top: 2px !important;
    width: 6px !important;
    height: 6px !important;
    border-radius: 50% !important;
    background-color: var(--accent-color) !important;
}

.review-radio-group span,
.rating-div.review-radio-group span,
.turnaround-radio-group span {
    color: #666 !important; /* Keep original color */
    font-size: 0.8rem !important; /* Keep original size */
    font-weight: 300 !important; /* Keep original weight */
}

/* Ensure radio buttons are aligned vertically */
.review-radio-group .form-group {
    display: block !important;
    margin-bottom: 8px !important;
}

/* Rating radio buttons should display horizontally */
.rating-div.review-radio-group {
    display: flex !important;
    flex-wrap: wrap !important;
    align-items: center !important;
    gap: 15px !important;
}

.rating-div.review-radio-group small {
    margin-right: 5px !important;
}

/* Turnaround Time radio buttons styling */
.turnaround-radio-group {
    display: flex !important;
    gap: var(--large-spacing) !important;
    align-items: center !important;
    flex-wrap: wrap !important;
}

/* Review options checkboxes styling - Consolidated common styles */
.vault-filter-label input[type="checkbox"],
.vault-price-label input[type="checkbox"],
.vault-category-label input[type="checkbox"],
.reader-filter-label input[type="checkbox"],
.author-filter-label input[type="checkbox"],
.review-options-checkbox {
    border: 1px solid #aaa !important; /* Thinner border */
    border-radius: 2px !important;
    width: 12px !important;
    height: 12px !important;
    appearance: none !important;
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
    background-color: #fff !important;
    position: relative !important;
    cursor: pointer !important;
    outline: none !important;
    margin-left: 5px !important;
    margin-right: 0 !important;
}

/* Style for checked state */
.vault-filter-label input[type="checkbox"]:checked,
.vault-price-label input[type="checkbox"]:checked,
.vault-category-label input[type="checkbox"]:checked,
.reader-filter-label input[type="checkbox"]:checked,
.author-filter-label input[type="checkbox"]:checked,
.review-options-checkbox:checked {
    background-color: var(--accent-color) !important;
    border-color: var(--accent-color) !important;
}

/* Add checkmark for checked state */
.vault-filter-label input[type="checkbox"]:checked::after,
.vault-price-label input[type="checkbox"]:checked::after,
.vault-category-label input[type="checkbox"]:checked::after,
.reader-filter-label input[type="checkbox"]:checked::after,
.author-filter-label input[type="checkbox"]:checked::after,
.review-options-checkbox:checked::after {
    content: '' !important;
    position: absolute !important;
    left: 4px !important;
    top: 1px !important;
    width: 4px !important;
    height: 7px !important;
    border: solid white !important;
    border-width: 0 2px 2px 0 !important;
    transform: rotate(45deg) !important;
}

/* Hide recommended days line */
.recommended-days {
    display: none !important;
}

/* ======================
   7. CARDS & COMPONENTS
   ====================== */
/* Base card style */
.card {
    border-radius: var(--card-border-radius) !important;
    background-color: var(--card-background) !important;
    border: var(--card-border-visibility) !important;
    margin-bottom: var(--card-margin) !important;
    overflow: hidden !important;
}


/* Global note card styling for both home and inner pages */
.note-card {
    background: #fff !important;
    width: 100%;
    margin-bottom: 1rem;
    border: 1px solid rgba(0, 0, 0, 0.125) !important;
    text-align: left !important;

    font-size: 0.9rem !important;
    font-weight: 300 !important;
    line-height: 1.4 !important;
    margin-bottom: 5px !important;
    color: #666 !important;
}


/* Add spacing to all profile sections except the first one */
#subscription-section,
#payment-method-section,
#invoices-section,
#security-section {
    margin-top: 60px !important; /* Add significant space above each section */
}

/* If there's a first section that shouldn't have the margin */
#profile-section {
    margin-top: 0 !important; /* No extra margin for the first section */
}

/* Ensure note content is left-aligned */
.note-content,
.note-date,
.reward-options,
.visible-content {
    text-align: left !important;
}

/* Literary Quote - used across the site - Consolidated */
.literary-quote,
.vault-literary-quote,
.reader-literary-quote,
.author-literary-quote {
    font-size: var(--quote-font-size);
    color: var(--quote-color) !important;
    margin-top: 10px !important;
    margin-bottom: 20px;
    max-width: var(--quote-max-width);
    margin-left: auto;
    margin-right: auto;
    text-align: center;
    font-weight: 300;
}

.quote-text {
    font-style: normal;
    font-size: var(--quote-font-size);
}

.quote-separator {
    font-style: normal;
    font-size: var(--quote-font-size);
}

.quote-source {
    font-style: italic;
    font-size: calc(var(--quote-font-size) - 0.1rem);
}

/* Quote container */
.quote-container {
    padding: 2rem 0;
    background: white;
}

/* Flash Message Styling - Consolidated */
.alert {
    margin: 10px 0;
    font-size: 14px;
    padding: 12px 20px;
    border: none !important; /* Remove all borders */
    border-radius: 0 !important; /* Remove rounded corners */
    font-weight: 300 !important; /* Keep original weight */
    box-shadow: none !important;
    background-image: none !important; /* Remove background image */
}

/* Ensure each alert type has the correct background color */
.alert-success {
    background-color: #d4edda !important; /* Light green background */
    color: #28a745 !important; /* Green text for success */
}

.alert-info {
    background-color: #e6f2ff !important; /* Pale blue background */
    color: #17a2b8 !important; /* Blue text for info */
}

.alert-warning {
    background-color: #fff3cd !important; /* Light yellow background */
    color: #856404 !important; /* Dark brown text for better readability on yellow background */
}

.alert-danger {
    background-color: #f8d7da !important; /* Light red background */
    color: #dc3545 !important; /* Red text for errors */
}

/* ======================
   8. BOOK-RELATED STYLES
   ====================== */
/* Book card header styles - consolidated */
.book-card-header {
    padding: 0 !important;
    height: auto !important;
    overflow: hidden !important;
    border-bottom: none !important;
    background-color: transparent !important;
    line-height: 0 !important;
    font-size: 0 !important;
    margin: 0 !important;
}

/* Book cover image styles - consolidated */
.book-card-header img {
    width: 100% !important;
    height: auto !important;
    object-fit: cover !important;
    display: block !important;
    margin: 0 !important;
    padding: 0 !important;
}

/* Book card body styles - consolidated */
.book-card-body {
    padding: 8px !important;
    font-size: 1rem !important;
    display: flex !important;
    flex-direction: column !important;
    height: 100% !important;
    margin: 0 !important;
}

/* Top section: Title and Summary - consolidated */
.book-card-top-section {
    margin-bottom: 3px !important;
    min-height: auto !important;
    padding: 0 !important;
    padding-top: 7px !important; /* Added padding to top section */
    font-weight: 400 !important;
}

/* Middle section: Book details - consolidated */
.book-card-middle-section {
    margin-bottom: 0px !important; /* Reduced from 3px to 0px */
    min-height: auto !important;
    padding: 0 !important;
}

/* Bottom section: Action buttons - consolidated */
.book-card-bottom-section {
    padding-top: 5px !important; /* Reduced from var(--card-margin) to 5px */
    min-height: auto !important;
    margin-top: auto !important;
    padding-bottom: 0 !important;
}

/* Book title styling - Consolidated */
.vault-book .card-body h4.card-title,
.reader-book .card-body h4.card-title,
.author-book .card-body h4.card-title,
.global-book-card h4, /* Added global */
.author-book-card h4 /* Added author-book-card */
 {
    font-size: 1.2rem !important; /* Standardized size */
    margin-bottom: 2px !important; /* Standardized margin */
    margin-top: 8px !important; /* Added top margin to create space above title */
    font-weight: 400 !important;
    color: black !important;
    line-height: 1.2 !important;
}

/* Book summary styling - Consolidated */
.vault-book .card-body p:first-of-type,
.reader-book .card-body p:first-of-type,
.author-book .card-body p:first-of-type,
.global-book-card p:first-of-type, /* Added global */
.author-book-card p:first-of-type /* Added author-book-card */
 {
    margin-bottom: var(--card-margin) !important; /* Use variable */
    margin-top: 0 !important; /* Ensure no top margin */
    line-height: 1.2 !important; /* Standardized line height */
    font-style: italic !important; /* Keep italic */
    font-weight: 300 !important; /* Keep weight */
}

/* General paragraph styling in book cards - Consolidated */
.vault-book .card-body p,
.reader-book .card-body p,
.author-book .card-body p,
.global-book-card p,
.author-book-card p {
    font-size: 0.9rem !important;
    font-weight: 300 !important;
    margin-bottom: 0.7rem !important; /* Increased from 0.5rem to 0.7rem */
}

/* Book type and turnaround time styling - Consolidated */
.book-type,
.turnaround-time {
    margin: 0 !important;
    padding: 0 !important;
    line-height: 1.6 !important; /* Increased from 1.2 to 1.6 */
    font-weight: var(--bold-font-weight) !important;
}

/* Fix for extra space in book cards */
.card-header {
    font-size: 0 !important;
    line-height: 0 !important;
    margin: 0 !important;
    padding: 0 !important;
}

.card-body {
    padding: 8px !important;
}

/* Ensure consistent spacing in all card sections */
.vault-book .card-body p,
.reader-book .card-body p,
.author-book .card-body p {
    line-height: 1.4 !important; /* Increased from 1.2 to 1.6 */
    margin: 0 0 0.3rem 0 !important; /* Increased bottom margin */
    padding: 0 !important;
}

/* Rating adjustments */
.rating {
    margin: 0 !important;
    padding: 0 !important;
}

.rating p {
    margin: 0 !important;
    padding: 0 !important;
    line-height: 1.2 !important;
}

/* Reduce all spacers */
.reader-card-spacer,
.author-card-spacer {
    height: 4px !important;
}

/* Vault book card specific adjustments */
.vault-card-top-section {
    margin-bottom: 5px !important; /* Reduced spacing */
    min-height: auto !important; /* Allow height to adjust to content */
}

.vault-card-middle-section {
    margin-bottom: 5px !important; /* Reduced spacing */
    min-height: auto !important; /* Allow height to adjust to content */
}

/* Vault card bottom section - ensure proper spacing */
.vault-card-bottom-section {
    padding-top: 0px !important; /* Reduced from 5px to 0px */
    min-height: auto !important; /* Allow height to adjust to content */
}

/* Card container adjustments - Consolidated */
.vault-book-container,
.reader-book-container,
.author-book-container {
    padding: 0 0px !important;
}

/* Card column adjustments - Consolidated */
.vault-book-card-column,
.reader-book-card-column,
.author-book-card-column {
    margin-bottom: 10px !important;
}

/* Book container styles - Consolidated */
.vault-books-container,
.reader-books-container,
.author-books-container {
    padding: 0 0px !important;
}

/* Book card styles - Consolidated */
.vault-book .card,
.reader-book .card,
.author-book .card {
    border-radius: 0 !important;
    margin-bottom: 10px !important;
    height: 100% !important;
    display: flex !important;
    flex-direction: column !important;
    /*box-shadow: 0 1px 3px rgba(0,0,0,0.1) !important;*/
    transition: box-shadow 0.3s ease !important;
    border: var(--card-border-visibility) !important; /* Keep border visibility */
}

/* Book card hover styles - Consolidated */
.vault-book .card:hover,
.reader-book .card:hover,
.author-book .card:hover {
    /*box-shadow: 0 3px 6px rgba(0,0,0,0.15) !important;*/ /* Keep commented out */
}

/* Remove padding in cover images containers - Consolidated */
.vault-book .card-header,
.reader-book .card-header,
.author-book .card-header {
    padding: 0 !important;
    height: 300px !important; /* Fixed height for all cover containers */
    overflow: hidden !important;
    border-radius: 0 !important;
}

/* Book cover image styles within card header - Consolidated */
.vault-book .card-header img,
.reader-book .card-header img,
.author-book .card-header img {
    width: 100% !important;
    height: 100% !important;
    object-fit: cover !important;
    border-radius: 0 !important;
}

/* Structure the card body into three sections with equal heights across a row - Consolidated */
.vault-book .card-body,
.reader-book .card-body,
.author-book .card-body {
    padding: var(--card-padding) !important;
    font-size: 0.8rem !important;
    line-height: var(--card-line-height) !important;
    display: flex !important;
    flex-direction: column !important;
    flex-grow: 1 !important;
    justify-content: space-between !important;
}

/* Top section: Title and Summary - fixed height - Consolidated */
.vault-card-top-section,
.reader-card-top-section,
.author-card-top-section {
    margin-bottom: var(--card-margin) !important;
    min-height: 80px !important;
    overflow: hidden !important;
}

/* Middle section: Book details - fixed height - Consolidated */
.vault-card-middle-section,
.reader-card-middle-section,
.author-card-middle-section {
    margin-bottom: var(--card-margin) !important;
    min-height: 140px !important;
}

/* Bottom section: View button - fixed height - Consolidated */
.vault-card-bottom-section,
.reader-card-bottom-section,
.author-card-bottom-section {
    padding-top: 5px !important; /* Reduced from var(--card-margin) to 5px */
    min-height: 25px !important;
}

/* Style View This Book button as simple text link - Consolidated */
.vault-card-bottom-section .btn-primary:not(.btn-sm),
.reader-card-bottom-section .btn-primary:not(.btn-sm),
.author-card-bottom-section .btn-primary:not(.btn-sm) {
    background: none !important;
    border: none !important;
    color: var(--accent-color) !important;
    text-decoration: underline !important;
    padding: 0 !important;
    margin-top: 0 !important;
    text-align: left !important;
    font-weight: 300 !important;
    box-shadow: none !important;
    /*font-size: 1rem !important;*/
}

/* To decrease the font size of the "View this book" buttons in the Vault page 
.vault-card-bottom-section .btn-text-link {
    font-size: 1.0rem !important; 
}
*/

/* Fix for View This Book button*/
.vault-card-bottom-section .btn-text-link {
    padding: 0 !important;
    margin: 0 !important;
    line-height: 1.2 !important;
    font-size: 1.0rem !important;  /* Add this line to make vault buttons smaller */
}


/* Hover state for View This Book button - Consolidated */
.vault-card-bottom-section .btn-primary:not(.btn-sm):hover,
.reader-card-bottom-section .btn-primary:not(.btn-sm):hover,
.author-card-bottom-section .btn-primary:not(.btn-sm):hover {
    background: none !important;
    color: var(--accent-color) !important;
}

/* Add specific hover state for small primary buttons */
.vault-card-bottom-section .btn-sm.btn-primary:hover,
.reader-card-bottom-section .btn-sm.btn-primary:hover,
.author-card-bottom-section .btn-sm.btn-primary:hover {
    background-color: var(--primary-color) !important;
    border-color: var(--primary-color) !important;
    color: var(--accent-color) !important;
}

/* Reduce space between individual book cards within rows - Consolidated */
.vault-book-card-column,
.reader-book-card-column,
.author-book-card-column {
    padding-left: 2px !important;
    padding-right: 2px !important;
    margin-bottom: 10px !important;
}

/* Ensure rows are properly aligned - Consolidated */
.vault-book-container,
.reader-book-container,
.author-book-container {
    display: flex !important;
    flex-wrap: wrap !important;
    align-items: stretch !important;
    margin: 0 -2px !important;
    width: 100% !important;
    justify-content: flex-start !important;
}

/* Additional fixes for vault book cards */
.vault-book {
    margin: 0 !important;
    padding: 0 !important;
}

.vault-book .card {
    margin: 0 !important;
    border-radius: 0 !important;
    /*box-shadow: 0 1px 3px rgba(0,0,0,0.1) !important;*/
    border: var(--card-border-visibility) !important;
}

/* Fix for the specific layout in the image */
.vault-book-card-column {
    display: flex !important;
    flex-direction: column !important;
}

/* Ensure the card takes up the full height */
.vault-book .card {
    height: 100% !important;
    display: flex !important;
    flex-direction: column !important;
    border: var(--card-border-visibility) !important;
}

/* Ensure the row inside the card doesn't add extra space */
.vault-book .card .row {
    margin: 0 !important;
    flex: 1 !important;
    display: flex !important;
}

/* Ensure the columns inside the row don't add extra space */
.vault-book .card .row .col-md-4,
.vault-book .card .row .col-md-8 {
    padding: 0 !important;
    display: flex !important;
    flex-direction: column !important;
}

/* Ensure the card body fills the available space */
.vault-book .card-body {
    flex: 1 !important;
    padding: 8px !important;
    display: flex !important;
    flex-direction: column !important;
}

/* Ensure the summary text doesn't add extra space */
.vault-book .card-body p.card-text {
    margin: 0 0 1px 0 !important;
    padding: 0 !important;
    line-height: 1.2 !important;
    max-height: 3.6em !important; /* Limit to 3 lines */
    overflow: hidden !important;
}

/* Ensure consistent height for all cards in a row */
.row.vault-book-container {
    display: flex !important;
    flex-wrap: wrap !important;
    align-items: stretch !important;
}

/* Additional fixes for reader book cards */
.reader-book {
    margin: 0 !important;
    padding: 0 !important;
}

.reader-book .card {
    border: var(--card-border-visibility) !important;
    margin-bottom: 20px !important;
    /*box-shadow: 0 2px 5px rgba(0,0,0,0.05) !important;*/
}

/* Ensure proper spacing in the row */
.reader-book .card .row {
    margin: 0 !important;
    padding: 0 !important;
}

/* Ensure the card header (image container) has no extra space */
.reader-book .card-header {
    padding: 0 !important;
    margin: 0 !important;
    /*border: none !important;*/
    line-height: 0 !important;
}

/* Ensure the card body has proper padding */
.reader-book .card-body {
    padding: 15px !important;
}

/* Adjust spacing for book title */
.reader-book .card-body h4.card-title {
    margin-top: 0 !important;
    margin-bottom: 10px !important;
}

/* Adjust spacing for book summary */
.reader-book .card-body p:first-of-type {
    margin-bottom: 15px !important;
}

/* Ensure proper spacing between book details */
.reader-book .card-middle-section p {
    margin-bottom: 5px !important;
}

/* Adjust spacing for the bottom section */
.reader-card-bottom-section {
    margin-top: 15px !important;
}

/* Ensure proper spacing in reader book cards */
.reader-book-card-column {
    margin-bottom: 20px !important;
}

/* Ensure proper image display in reader cards */
.reader-book .card-header img {
    width: 100% !important;
    height: auto !important;
    display: block !important;
}

/* Ensure proper text display in reader cards */
.reader-book .card-body p {
    line-height: 1.4 !important;
    margin-bottom: 5px !important;
}

/* Ensure proper spacing for the View Book button */
.reader-card-bottom-section .btn-text-link {
    margin-top: 5px !important; /* Reduced from 10px to 5px */
    display: inline-block !important;
}

/* ======================
   9. PAGE-SPECIFIC STYLES
   ====================== */

/* 9.1. Vault Styles */
/* Hero section - Consolidated */
.vault-hero-section,
.reader-hero-section,
.author-hero-section {
    padding-bottom: 0 !important;
}

.vault-hero-image-container,
.reader-hero-image-container,
.author-hero-image-container {
    position: relative;
    margin-bottom: 0 !important;
    margin-top: 5px !important;
    transition: opacity 0.5s ease;
    width: 240px;
}

.vault-hero-image-container img,
.reader-hero-image-container img,
.author-hero-image-container img {
    width: 100%;
    height: auto;
    object-fit: cover;
}

/* UNDER IMAGE STYLE - Simple text below the image - Consolidated */
.under-image-name.vault-image-caption,
.under-image-name.reader-image-caption,
.under-image-name.author-image-caption,
.vault-image-caption,
.reader-image-caption,
.author-image-caption {
    position: static !important;
    background: transparent !important;
    color: #000 !important;
    padding: 0.1rem 0 !important;
    text-align: center !important;
    font-size: 0.75rem !important;
    font-weight: 300 !important;
    margin-top: 0.1rem !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    width: 150% !important;
    margin-left: -25% !important;
}

/* Spacing utilities - Consolidated */
.vault-padding-medium,
.reader-padding-medium,
.author-padding-medium {
    padding: 0.0rem !important;
}

.vault-no-padding-bottom,
.reader-no-padding-bottom,
.author-no-padding-bottom {
    padding-bottom: 0 !important;
}

/* Filter section  SPACE ABOVE A HERO RANDOM IMAGE - Consolidated */
.vault-filter-container,
.reader-filter-container,
.author-filter-container {
    margin-top: 1.5rem !important;
    margin-bottom: 1rem !important;
}

.vault-filter-options {
    margin-bottom: 1rem !important;
    gap: 10px;
}

.vault-filter-label {
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    gap: 10px !important;
    font-size: var(--label-font-size) !important;
    /*font-size: 0.75rem !important; /* Set specific font size */
}

/* Sidebar filter styles */
.vault-sidebar-column {
    padding: 0 3px !important;
}

.vault-sidebar-heading {
    font-size: var(--heading-font-size) !important;
    margin-bottom: 10px !important;
    font-weight: 300 !important;
    padding-bottom: 5px !important;
    /*border-bottom: 1px solid #eee !important;*/
    width: 100% !important;
}

.vault-sidebar-label {
    font-size: var(--label-font-size) !important;
    gap: var(--label-gap) !important;
    margin-bottom: var(--label-margin-bottom) !important;
    display: flex !important;
    align-items: center !important;
}

/* Price Range specific styles */
.vault-price-column {
    display: flex !important;
    flex-direction: column !important;
    align-items: flex-end !important;
    padding-left: 0 !important;
    padding-right: 2px !important;
}

.vault-price-column .vault-sidebar-heading {
    align-self: flex-end !important;
    text-align: right !important;
}

/* Price label container */
.vault-price-label {
    display: flex !important;
    justify-content: flex-end !important;
    align-items: center !important;
    /* justify-content: center !important; - Removed duplicate */
}

/* Price text styling */
.vault-price-label span {
    display: inline-block !important;
}

/* Categories specific styles */
.vault-category-column {
    display: flex !important;
    flex-direction: column !important;
    align-items: flex-start !important;
    padding-right: 0 !important;
    padding-left: 2px !important;
}

.vault-category-label {
    flex-direction: row-reverse !important; /* Text first, then checkbox */
    justify-content: flex-start !important;
}

.vault-category-label span {
    margin-right: 5px !important;
}

/* Category styling for filter buttons */
.vault-category-button {
    background-color: var(--primary-color);
    border-radius: 4px;
}

.vault-category-button label {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 5px 15px;
}

.vault-category-button label span {
    text-align: center;
    padding: 5px 15px;
    display: block;
}

.vault-category-button label input {
    position: absolute;
    display: none;
    color: #fff !important;
}

.vault-category-button label input+span {
    color: #fff;
}

.vault-category-button input:checked+span {
    color: #ffffff;
}

/* Pricing div styling */
.pricingdiv {
    display: none;
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease-out;
}

.pricingdiv.show {
    display: flex !important;
    max-height: 500px;
    transition: max-height 0.5s ease-in;
}

#MainVaultContainer #error {
    margin: 0 15px !important;
    width: calc(100% - 30px) !important;
}

/* 9.2. Home Page Styles */
/* Work box styles */
.work-box {
    display: flex;
    flex-direction: column;
    gap: 20px;
    transition: 0.6s;
    padding-bottom: 30px;
    border: none;
    overflow: visible;
    width: 100%;
    height: auto;
}

.work-box-image-container {
    height: 300px;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
    position: relative;
    margin-bottom: 1rem;
}

.work-box-image-container img {
    height: 100%;
    width: auto;
    max-width: none;
    object-fit: cover;
    object-position: center;
    position: relative;
    z-index: 1;
}

.work-box-content {
    padding: 0.5rem 0;
    text-align: center;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
}

.work-box-title {
    color: #4e7ac7;
    font-size: 1rem;
    font-weight: 400;
    margin-bottom: 0.5rem;
}

.work-box-description-container {
    margin-bottom: 0.5rem;
    position: relative;
    overflow: hidden;
    transition: height 0.3s ease-in-out;
}

.work-box-description {
    color: #666;
    font-size: 0.9rem;
    line-height: 1.5;
    margin: 0;
}

.truncated-description {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    max-height: 4.5em;
    line-clamp: 3;
}

.work-box-description-container.expanded {
    max-height: none;
    -webkit-line-clamp: unset;
    line-clamp: unset;
}

.learn-more-content {
    background-color: transparent;
}

.learn-more-toggle {
    color: var(--accent-color);
    text-decoration: underline;
    cursor: pointer;
    display: inline-block;
    margin-top: 0.5rem;
}

/* 9.3. Reader Styles */
/* Reader section headings */
.reader-section-heading {
    font-size: var(--section-heading-font-size) !important;
    color: var(--accent-color) !important;
    font-weight: 300 !important;
    margin-bottom: 15px !important;
    /*width: 300px !important;*/
    padding-bottom: 5px !important;
    border-bottom: 1px solid #eee !important;
}

/* Reader card middle section - ensure proper spacing between elements */
.reader-card-middle-section p {
    margin-bottom: 5px !important;
    line-height: 1.6 !important;
}

/* Style for book status and other info in reader cards */
.reader-card-middle-section .book-type,
.reader-card-middle-section .turnaround-time {
    font-weight: var(--bold-font-weight) !important;
}

/* Style for rating stars */
.reader-card-middle-section .rating i {
    vertical-align: middle !important;
    margin-top: 3px !important;
}

.reader-card-middle-section .rating small {
    color: #666 !important;
    margin-left: 13px !important;
}

/* Reader history items - Consolidated */
.reader-history-item,
.author-history-item,
.review-history-item {
    margin-bottom: 15px !important;
    padding-bottom: 15px !important;
    border-bottom: 1px solid #eee !important;
}

.reader-history-image,
.author-history-image,
.review-history-image {
    width: 100px !important;
    height: auto !important;
    overflow: hidden !important;
}

.reader-history-content,
.author-history-content,
.review-history-content {
    width: 75% !important;
}

.reader-history-title p,
.author-history-title p,
.review-history-title p {
    font-weight: 300 !important;
    margin-bottom: 5px !important;
    font-size: 1.0rem !important; /* Standardized size */
}

.reader-history-summary,
.author-history-summary,
.review-history-summary {
    margin-bottom: 5px !important;
    font-size: 0.8rem !important; /* Standardized size */
}

.reader-history-date,
.author-history-date,
.review-history-date {
    font-size: 0.8rem !important;
    color: #666 !important;
}

/* Quiz results text styling */
.quiz-results-text {
    font-size: 0.8rem !important;
    color: #666 !important;
    margin-bottom: 5px !important;
    font-weight: 300 !important;
}

/* Reviews History section improvements */
.review-history-item {
    border: 1px solid #eee !important;
    border-radius: 5px !important;
    padding: 15px !important;
    margin-bottom: 15px !important;
    background-color: #fff !important;
}

.review-history-content {
    display: flex !important;
    flex-direction: column !important;
    gap: 10px !important;
}

/* Rating display styling */
.stars-container {
    margin-right: 8px !important;
}

.rating-text {
    font-size: 0.8rem !important;
    color: #666 !important;
    font-weight: 300 !important;
}

/* Amazon link smaller font in Reviews History */
.amazon-link-small {
    font-size: 0.8rem !important;
}

/* Reviews History structured layout */
.review-header {
    display: flex !important;
    justify-content: space-between !important;
    align-items: flex-start !important;
    margin-bottom: 15px !important;
    padding-bottom: 10px !important;
    border-bottom: 1px solid #eee !important;
}

.review-rating-section,
.review-status-section {
    flex: 1 !important;
}

.review-status-section {
    text-align: right !important;
}

.review-item-heading {
    font-size: 0.9rem !important;
    font-weight: 500 !important;
    color: var(--accent-color) !important;
    margin-bottom: 5px !important;
}

.review-details {
    display: flex !important;
    flex-direction: column !important;
    gap: 10px !important;
}

.review-detail-item {
    display: flex !important;
    align-items: flex-start !important;
    gap: 10px !important;
}

.review-detail-heading {
    font-size: 0.8rem !important;
    font-weight: 500 !important;
    color: #333 !important;
    margin: 0 !important;
    min-width: 120px !important;
    flex-shrink: 0 !important;
}

.review-detail-text {
    font-size: 0.8rem !important;
    color: #666 !important;
    margin: 0 !important;
    font-weight: 300 !important;
}

/* Responsive adjustments for Reviews History */
@media (max-width: 768px) {
    .review-header {
        flex-direction: column !important;
        gap: 10px !important;
    }

    .review-status-section {
        text-align: left !important;
    }

    .review-detail-item {
        flex-direction: column !important;
        gap: 2px !important;
    }

    .review-detail-heading {
        min-width: auto !important;
    }
}

/* Spacer between book status and points */
.reader-card-spacer {
    height: 10px !important; /* Reduced from 10px */
}

/* 9.4. Author Styles */
/* Author section headings */
.author-section-heading {
    font-size: var(--section-heading-font-size) !important;
    color: var(--accent-color) !important;
    font-weight: 300 !important;
    margin-bottom: 15px !important;
    /*width: 300px !important;*/
    padding-bottom: 5px !important;
    border-bottom: 1px solid #eee !important;
}

/* Author book view specific styles */
.author-container .page-submenu .nav-link {
    font-size: 12px !important; /* Smaller font for author book view submenu on desktop */
}

/* Responsive override for mobile */
@media (max-width: 768px) {
    .author-container .page-submenu .nav-link {
        font-size: var(--nav-font-size) !important; /* Use the larger mobile font size */
    }
}

/* Author card middle section - ensure proper spacing between elements */
.author-card-middle-section p {
    margin-bottom: 5px !important;
    line-height: 1.4 !important;
}

/* Style for book status and other info in author cards */
.author-card-middle-section .book-type,
.author-card-middle-section .turnaround-time {
    font-weight: var(--bold-font-weight) !important;
}

/* Spacer between book status and points */
.author-card-spacer {
    height: 10px !important;
}

/* Author book title styling */
.author-book-title {
    font-weight: 300 !important;
    color: black !important;
    margin-top: 1.5rem !important;
    margin-bottom: 1.5rem !important;
    font-size: 2rem !important;
}

/* Hero section styling for author book page */
.author-hero-section {
    padding-bottom: 1.5rem !important;
}

/* Author book card styles */
.author-book-card {
    border: 1px solid #eee !important;
    padding: 15px !important;
    margin-bottom: 20px !important;
    background-color: #fff !important;
}

.author-book-card h4 {
    font-size: 1.2rem !important;
    font-weight: 400 !important;
    margin-bottom: 0.5rem !important;
}

.author-book-card p {
    font-size: 0.9rem !important;
    font-weight: 300 !important;
    line-height: 1.4 !important;
    margin-bottom: 0.5rem !important;
}

.author-book-card .meta-data p {
    font-weight: 300 !important;
    margin-bottom: 0.3rem !important;
}

.author-book-card .meta-data p b {
    font-weight: 400 !important;
}

/* Author specific button styles */
.author-container .btn-sm.btn-primary {
    background-color: var(--accent-color) !important;
    border-color: var(--accent-color) !important;
    color: white !important;
    text-decoration: none !important;
    padding: 0.5rem 1rem !important;
    font-size: 0.875rem !important;
}

.author-container .btn-sm.btn-primary:hover {
    background-color: white !important;
    border-color: var(--primary-color) !important;
    color: var(--accent-color) !important;
}

/* Add space between buttons */
.author-container .btn-sm.btn-primary:not(:last-child) {
    margin-right: 25px !important; /* Increased from 15px */
}

/* Target the View Book, Boost Book, Edit Book links specifically */
a[href*="view-book"],
a[href*="boost-book"],
a[href*="edit-book"],
.author-book a.btn-text-link,
.author-book-card a.btn-text-link {
    margin-right: 20px !important;
    display: inline-block !important;
}

/* 9.5. Review Page Styles */
/* Review section headings */
.review-section-heading {
    font-size: var(--section-heading-font-size) !important;
    color: var(--accent-color) !important;
    font-weight: 300 !important;
    margin-bottom: 15px !important;
    /*width: 300px !important;*/
    padding-bottom: 5px !important;
    border-bottom: 1px solid #eee !important;
}

/* Review text styles */
.review-text {
    font-size: 0.9rem !important;
    font-weight: 300 !important;
    line-height: 1.4 !important;
    margin-bottom: 5px !important;
    color: #666 !important;
}

.review-question {
    font-size: 0.9rem !important;
    line-height: 1.4 !important;
    margin-bottom: 8px !important;
    font-weight: 400 !important;
}

.review-answer {
    font-size: 0.8rem !important;
    line-height: 1.4 !important;
    margin-bottom: 3px !important;
    color: #666 !important;
}

.review-heading {
    font-size: 1.0rem !important;
    font-weight: 400 !important;
    margin-bottom: 5px !important;
}

/* Global styles for About This Book section */
.about-heading {
    font-size: 1.0rem !important;
    font-weight: 400 !important;
    margin-bottom: 5px !important;
}

/* Review validation status styles */
.review-validation-status {
    display: flex !important;
    align-items: center !important;
    gap: 10px !important;
}

/* Text-only validation status indicators (no background, just colored text) */
.validation-text-rejected {
    color: #f50d24 !important; /* Red */
    font-weight: 300 !important;
    font-size: 0.9rem !important;
}

.validation-text-approved {
    color: #36b92a !important; /* Green */
    font-weight: 300 !important;
    font-size: 0.9rem !important;
}

.validation-text-partial {
    color: #ff7a0c !important; /* Orange */
    font-weight: 300 !important;
    font-size: 0.9rem !important;
}

.validation-text-pending {
    color: #fa7609 !important; /* Orange */
    font-weight: 300 !important;
    font-size: 0.9rem !important;
}

.validation-text-link-submitted {
    color: #757575 !important; /* Blue */
    font-weight: 300 !important;
    font-size: 0.9rem !important;
}

/* Submit link button - neat and inline with validation text */
.submit-link-btn {
    background: none !important;
    border: 1px solid #007bff !important;
    color: #007bff !important;
    padding: 2px 8px !important;
    font-size: 0.8rem !important;
    border-radius: 3px !important;
    text-decoration: none !important;
    display: inline-block !important;
    margin-left: 8px !important;
    transition: all 0.2s ease !important;
    font-weight: 300 !important;
}

.submit-link-btn:hover {
    background-color: #007bff !important;
    color: white !important;
    text-decoration: none !important;
}

/* Legacy badge styles - keeping for backward compatibility but updating colors */
.badge-soft-success {
    background-color: #d4edda !important;
    color: #155724 !important;
    border: 1px solid #c3e6cb !important;
}

.badge-soft-warning {
    background-color: #fff3cd !important;
    color: #856404 !important;
    border: 1px solid #ffeaa7 !important;
}

.badge-soft-danger {
    background-color: #f8d7da !important;
    color: #721c24 !important;
    border: 1px solid #f5c6cb !important;
}

.badge-soft-info {
    background-color: #d1ecf1 !important;
    color: #0c5460 !important;
    border: 1px solid #bee5eb !important;
}

/* Submit link modal styles */
.modal-content {
    border-radius: 12px !important;
    border: none !important;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15) !important;
}

.modal-header {
    border-bottom: 1px solid #dee2e6 !important;
    padding: 1.5rem 2rem !important;
    background-color: #f8f9fa !important;
    border-radius: 12px 12px 0 0 !important;
}

.modal-body {
    padding: 2rem !important;
}

.modal-body p {
    color: #666 !important;
    line-height: 1.6 !important;
}

.modal-body .form-control {
    border: 2px solid #e9ecef !important;
    border-radius: 8px !important;
    padding: 12px 16px !important;
    font-size: 1rem !important;
}

.modal-body .form-control:focus {
    border-color: #ff6b35 !important;
    box-shadow: 0 0 0 0.2rem rgba(255, 107, 53, 0.25) !important;
}

.modal-body .form-label {
    color: #495057 !important;
    margin-bottom: 8px !important;
}

.modal-body h5 {
    color: #495057 !important;
    font-weight: 600 !important;
}

.about-text {
    font-size: 0.9rem !important;
    font-weight: 300 !important;
    line-height: 1.4 !important;
    margin-bottom: 25px !important;
    color: #666 !important;
}

/* Ensure radio buttons are aligned vertically */
.review-radio-group .form-group {
    display: block !important;
    margin-bottom: 8px !important;
}

/* 9.6. Activity Section Styles */
.activity-text {
    font-size: 0.8rem !important;
    font-weight: 300 !important;
    line-height: 1.4 !important;
    margin-bottom: 5px !important;
    color: #666 !important;
}

.totalRewards {
    font-size: 1.0rem !important;
    font-weight: 300 !important;
    color: var(--accent-color) !important;
    margin-bottom: 1rem !important;
}

.totalRewards h5 {
    font-size: 1.2rem !important;
    font-weight: 300 !important;
    margin-top: 0.5rem !important;
}

/* Review Options Section Styles */
/* Turnaround Time radio buttons styling */
.turnaround-radio-group {
    display: flex !important;
    gap: var(--large-spacing) !important;
    align-items: center !important;
    flex-wrap: wrap !important;
}

/* Global book card styles - Consolidated */
.global-book-card,
.vault-book .card,
.reader-book .card,
.author-book .card,
.author-book-card {
    border: var(--card-border-visibility) !important;
    padding: 15px !important;
    margin-bottom: 20px !important; /* Standardized margin */
    background-color: #fff !important;
}

/* Book summary specific styling */
.book-summary {
    font-style: italic !important;
    display: -webkit-box !important;
    -webkit-line-clamp: 2 !important;
    -webkit-box-orient: vertical !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    max-height: 2.6em !important; /* 2 lines x 1.3 line-height */
    line-height: 1.3 !important;
}

/* Table header styling */
.table-header {
    font-size: 1.0rem !important;
    font-weight: 400 !important;
    color: var(--accent-color) !important;
}

/* SINGLE BOOK VIEW COVER IMAGE (3:4 Ratio) - Consolidated */
.vault-container .reader-book .card-header,
.reader-container .reader-book .card-header,
.turn-in-review .reader-book .card-header,
.review-container .reader-book .card-header,
.reader-book .card-header, /* Added reader-book */
.author-book .card-header, /* Added author-book */
.review-container .card-header /* Added review-container */
 {
    height: auto !important;
    width: 225px !important; /* Standardized width */
    max-width: 225px !important;
    min-width: 225px !important;
    overflow: hidden !important;
    padding: 0 !important;
    margin-left: auto !important;
    margin-right: auto !important; /* Centered */
    border-radius: 0 !important;
    float: none !important; /* Ensure no float */
}

.vault-container .reader-book .card-header img,
.reader-container .reader-book .card-header img,
.turn-in-review .reader-book .card-header img,
.review-container .reader-book .card-header img,
.reader-book .card-header img, /* Added reader-book */
.author-book .card-header img, /* Added author-book */
.review-container .card-header img /* Added review-container */
 {
    width: 100% !important;
    height: auto !important;
    object-fit: contain !important; /* Ensure full image is visible */
    border-radius: 0 !important;
    display: block !important;
    margin: 0 !important;
    padding: 0 !important;
    aspect-ratio: 3/4 !important;
}

/* Force text alignment to left in single book views - including review page - Consolidated */
.vault-container .reader-book .card-body,
.reader-container .reader-book .card-body,
.turn-in-review .reader-book .card-body,
.bg-light .reader-book .card-body,
.bg-light p,
.bg-light h4,
.bg-light div,
.turn-in-review p,
.turn-in-review h4,
.turn-in-review div {
    text-align: left !important;
}

/* Author book view specific fixes */
.author-book-card {
    background-color: white !important;
}

/* Ensure all book cards have white background */
.author-book-card,
.author-book .card,
.reader-book .card,
.vault-book .card,
.global-book-card,
.book-card {
    background-color: white !important;
}

/* Force display of action buttons in author book view - Consolidated */
.author-book-card .btn,
.author-book .btn,
.author-book-card a[href*="amazon"],
.author-book a[href*="amazon"],
.author-book-card a.btn-primary,
.author-book a.btn-primary,
.author-book-card button.btn-primary,
.author-book button.btn-primary,
.author-container .btn-sm.btn-primary {
    display: inline-block !important;
    visibility: visible !important;
    opacity: 1 !important;
    margin: 5px 5px 5px 0 !important;
    position: static !important;
}

/* Add specific button container for author view if missing */
.author-book-card:after,
.author-book:after {
    content: "" !important;
    display: block !important;
    clear: both !important;
    height: 50px !important; /* Space for buttons */
}

/* Ensure action buttons container is visible - Consolidated */
.author-book-card .action-buttons,
.author-book .action-buttons,
.author-book-card .button-container,
.author-book .button-container,
.card-body .d-flex, /* Added from mobile section */
.card-body .action-buttons, /* Added from mobile section */
.card-body .button-container, /* Added from mobile section */
.vault-card-bottom-section, /* Added from mobile section */
.reader-card-bottom-section /* Added from mobile section */
 {
    display: block !important; /* Changed to block for better control */
    visibility: visible !important;
    opacity: 1 !important;
    margin-top: 5px !important; /* Reduced from 15px to 5px */
    text-align: left !important; /* Default to left, mobile overrides to center */
    width: 100% !important; /* Ensure full width */
}

/* Override any hidden elements that might contain buttons - Consolidated */
.card-body .d-none,
.card-body .hidden,
.card-body [style*="display: none"],
.card-body [style*="visibility: hidden"],
.card-body [style*="opacity: 0"] {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* ======================
   10. UTILITY CLASSES
   ====================== */
.text-primary {
    color: var(--primary-color) !important;
}

.text-accent {
    color: var(--accent-color) !important;
}

.bg-primary {
    background-color: var(--primary-color) !important;
}

.bg-accent {
    background-color: var(--accent-color) !important;
}

/* Preserve existing bg-light class behavior but override with our variable */
.bg-light {
    background-color: white !important;
}

/* ======================
   10. FEEDBACK FORM STYLES
   ====================== */

/* Feedback section container */
.feedback-section {
    margin-bottom: 30px;
    padding: 20px 0;
    /*border-top: 1px solid #eee;*/
}

/* Feedback flash message - blue background, 50% width */
.feedback-flash-message {
    background-color: var(--accent-color) !important;
    color: white;
    padding: 15px 20px;
    border-radius: 5px;
    margin: 0 auto 15px auto;
    width: 50%;
    text-align: center;
    font-size: 0.8rem;
    line-height: 1.4;
    border: none;
}

/* Knowledge Base article link styling */
.knowledge-base-link {
    color: #ff8c00 !important; /* Orange color */
    text-decoration: underline !important;
    transition: color 0.3s ease !important;
}

.knowledge-base-link:hover {
    color: #e67e00 !important; /* Darker orange on hover */
    text-decoration: underline !important;
}

/* Vault flash messages - 90% width of container */
.vault-flash-message {
    width: 90% !important;
    margin: 0 auto 15px auto !important;
}

/* Apply vault styling to all flash messages when displayed in vault page, but NOT on book preview pages */
.vault-container:not(.book-preview-container) .alert {
    width: 90% !important;
    margin: 0 auto 15px auto !important;
}

/* Feedback toggle button container */
.feedback-toggle-container {
    text-align: center;
    margin-bottom: 15px;
}

/* Feedback toggle button - styled like "Show more" buttons from home.css */
.feedback-toggle-btn {
    background: none;
    border: none;
    color: var(--accent-color);
    cursor: pointer;
    font-size: 14px;
    text-decoration: underline;
    padding: 0;
    margin: 0;
    transition: color 0.3s ease;
}

.feedback-toggle-btn:hover {
    color: var(--primary-color);
}

/* Feedback form container */
.feedback-form-container {
    max-width: 600px;
    margin: 0 auto;
    padding: 20px;
    background-color: transparent !important;
    border-radius: 5px;
    border: 1px solid var(--accent-color);
    color: #000;
}

/* Override any Bootstrap form-control background */
.feedback-form-container .form-control {
    background-color: #fff !important;
}

/* Feedback textarea */
.feedback-textarea {
    width: 100%;
    min-height: 100px;
    padding: 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 14px;
    line-height: 1.5;
    resize: vertical;
    margin-bottom: 15px;
    color: #000;
    background-color: #fff;
}

.feedback-textarea:focus {
    border-color: var(--accent-color);
    outline: none;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Feedback submit button - using primary button style */
.feedback-submit-btn {
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.feedback-submit-btn:hover {
    background-color: var(--accent-color);
}

/* Responsive adjustments for feedback form */
@media (max-width: 768px) {
    .feedback-flash-message {
        width: 90%;
        font-size: 0.8rem;
        padding: 12px 15px;
    }

    .feedback-form-container {
        margin: 0 10px;
        padding: 15px;
    }

    .feedback-textarea {
        font-size: 16px; /* Prevent zoom on iOS */
    }
}




/* ======================
   11. RESPONSIVE STYLES
   ====================== */
@media (max-width: 768px) {
    :root {
        --label-font-size: 0.85rem;
        --heading-font-size: 1rem;
        --nav-font-size: 18px; /* Increased from 14px for mobile */
        --user-font-size: 18px; /* Increased from 14px for mobile */
    }

    .nav-radio-group {
        justify-content: center;
    }

    .mobmenu {
        margin-top: 0;
        padding-top: 0;
        display: flex; /* Show mobile menu */
        justify-content: flex-end;
        width: 100%;
    }

    .mobmenu i {
        font-size: 25px;
        color: var(--accent-color);
        cursor: pointer;
    }

    .mobmenu #menu {
        width: 100vw;
        height: 100vh;
        position: fixed;
        background: white;
        z-index: 99;
        top: 0;
        left: -100%; /* Hidden by default */
        transition: 0.5s ease-in-out;
    }

    #menu {
        list-style-type: none !important;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 60px;
        flex-direction: column;
    }

    #menu a,
    #menu button {
        font-size: 16px !important;
        font-weight: var(--normal-font-weight) !important;
        color: var(--nav-text-color) !important;
        text-transform: none !important;
        letter-spacing: normal !important;
    }

    #menu a.active,
    #menu button.active {
        color: var(--accent-color) !important;
        font-weight: var(--bold-font-weight) !important;
    }

    #menu a:hover,
    #menu button:hover {
        color: var(--nav-hover-color) !important;
    }

    #menu .menu-toggler {
        position: absolute;
        top: 0px;
        right: 0;
    }

    #menu.active {
        left: 0%; /* Show menu */
    }

    .deskHEader {
        display: none; /* Hide desktop header */
    }

    /* Mobile notification and balance styles */
.mobile-user-info {
    width: 100%;
    display: flex;
    justify-content: center;
    margin: 10px 0;
}

.mobile-notification-balance {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
    flex-direction: row; /* Ensure horizontal alignment */
    flex-wrap: nowrap; /* Prevent wrapping */
    width: auto; /* Allow container to size to content */
}

.mobile-notification-balance .header-notification {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.mobile-notification-balance .header-wallet-balance {
    margin: 0;
    font-size: var(--user-font-size);
    display: flex;
    align-items: center;
    gap: 5px;
    white-space: nowrap; /* Prevent wrapping */
}

.mobile-notification-balance .header-wallet-balance i {
    color: red;
}

.mobile-notification-balance .notification-container {
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    z-index: 9999;
    width: 300px; /* Set fixed width */
    max-height: 400px;
    overflow-y: auto;
}

/* Mobile notification icon styling */
#showMobileNotification {
    font-size: 18px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Ensure notification badge is visible on mobile */
.mobile-notification-balance .notification-badge {
    position: absolute;
    top: -6px;
    right: -6px;
    background-color: red;
    color: white;
    font-size: 10px;
    min-width: 16px;
    height: 16px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 400;
    padding: 1px;
}


    /* Make sidebars stack on mobile - Consolidated */
    .vault-price-column,
    .vault-category-column {
        width: 100% !important;
        max-width: 100% !important;
        flex: 0 0 100% !important;
        margin-bottom: 15px !important;
        align-items: center !important;
    }

    .vault-price-column .vault-sidebar-heading,
    .vault-category-column .vault-sidebar-heading {
        text-align: center !important;
        align-self: center !important;
    }

    .vault-price-label,
    .vault-category-label {
        justify-content: center !important;
    }

    /* Make book cards full width on mobile while maintaining proportions - Consolidated */
    .vault-book-card-column,
    .reader-book-card-column, /* Added reader */
    .author-book-card-column /* Added author */
     {
        -ms-flex: 0 0 100%;
        flex: 0 0 100%;
        max-width: 100%;
        padding-left: 5px !important; /* Standardized padding */
        padding-right: 5px !important; /* Standardized padding */
        margin-bottom: 1.5rem !important; /* Standardized margin */
    }

    /* Ensure book covers maintain proportions on mobile - Consolidated */
    .vault-book-card-column .card-header,
    .reader-book-card-column .card-header,
    .author-book-card-column .card-header {
        text-align: center !important;
        width: 100% !important;
        padding: 0 !important;
        height: auto !important;
        max-width: 100% !important;
        aspect-ratio: unset !important; /* Remove fixed aspect ratio */
    }

    .vault-book-card-column .card-header img,
    .reader-book-card-column .card-header img,
    .author-book-card-column .card-header img {
        display: block !important;
        margin-left: auto !important;
        margin-right: auto !important;
        max-width: 100% !important;
        height: auto !important;
        object-fit: cover !important; /* Use cover for consistency */
        aspect-ratio: 3/4 !important; /* Apply aspect ratio here */
    }

    /* Ensure text remains left-aligned in card body on mobile - Consolidated */
    .vault-book-card-column .card-body,
    .reader-book-card-column .card-body,
    .author-book-card-column .card-body {
        padding: 15px !important; /* Standardized padding */
        text-align: left !important; /* CRITICAL: Ensure text remains left-aligned */
        width: 100% !important;
    }

    /* Center button container on mobile - Consolidated */
    .vault-book-card-column .book-card-bottom-section,
    .reader-book-card-column .book-card-bottom-section,
    .author-book-card-column .book-card-bottom-section {
        text-align: center !important;
        margin-top: 10px !important;
        width: 100% !important;
    }

    /* Mobile adjustments for single book view - Consolidated */
    .reader-book .card-header,
    .author-book .card-header,
    .vault-container .reader-book .card-header,
    .reader-container .reader-book .card-header,
    .turn-in-review .reader-book .card-header,
    .review-container .reader-book .card-header {
        margin-left: auto !important;
        margin-right: auto !important;
        float: none !important;
        max-width: 100% !important; /* Changed from 225px to 100% to fill screen width */
        width: 100% !important;
        min-width: unset !important; /* Remove minimum width constraint */
    }

    /* Make book cover images fill the full width on mobile for inner pages */
    .reader-book .card-header img,
    .author-book .card-header img,
    .vault-container .reader-book .card-header img,
    .reader-container .reader-book .card-header img,
    .turn-in-review .reader-book .card-header img,
    .review-container .reader-book .card-header img {
        width: 100% !important;
        max-width: 100% !important;
        height: auto !important;
        object-fit: cover !important;
        display: block !important;
        margin: 0 auto !important;
    }

    /* Handle author book page layout - make book cover column full width on mobile */
    .author-book-card .col-md-4,
    .author-container .col-md-4 {
        -ms-flex: 0 0 100% !important;
        flex: 0 0 100% !important;
        max-width: 100% !important;
        margin-bottom: 20px !important;
    }

    /* Make author book cover images fill screen width on mobile */
    .author-book-card img.img-fluid,
    .author-container img.img-fluid {
        width: 100% !important;
        max-width: 100% !important;
        height: auto !important;
        object-fit: cover !important;
        display: block !important;
        margin: 0 auto !important;
    }

    /* Handle reader-single-book page layout - make book cover column full width on mobile */
    .reader-container .col-md-3 {
        -ms-flex: 0 0 100% !important;
        flex: 0 0 100% !important;
        max-width: 100% !important;
        margin-bottom: 20px !important;
    }

    /* Make reader-single-book cover images fill screen width on mobile */
    .reader-container .col-md-3 img.img-fluid {
        width: 100% !important;
        max-width: 100% !important;
        height: auto !important;
        object-fit: cover !important;
        display: block !important;
        margin: 0 auto !important;
    }

    /* Handle history sections - make book cover images larger on mobile */
    .reader-history-image,
    .review-history-image {
        width: 100% !important;
        max-width: 200px !important;
        margin: 0 auto 15px auto !important;
        display: block !important;
    }

    .reader-history-image img,
    .review-history-image img {
        width: 100% !important;
        max-width: 100% !important;
        height: auto !important;
        object-fit: cover !important;
        display: block !important;
        margin: 0 auto !important;
    }

    /* Make history items stack vertically on mobile */
    .reader-history-item,
    .review-history-item {
        flex-direction: column !important;
        text-align: center !important;
    }

    .reader-history-content,
    .review-history-content {
        width: 100% !important;
        text-align: center !important;
        margin-top: 10px !important;
    }

    /* Force display of buttons in mobile view - Consolidated */
    .vault-container a.btn,
    .vault-container button.btn,
    .reader-container a.btn,
    .reader-container button.btn,
    a[href*="amazon"],
    a.btn-text-link,
    button.btn-primary,
    a.btn-primary {
        display: inline-block !important;
        visibility: visible !important;
        opacity: 1 !important;
        position: static !important;
        margin: 5px !important;
        width: auto !important;
        max-width: 100% !important;
    }

    /* Ensure button container is visible - Consolidated */
    .card-body .d-flex,
    .card-body .action-buttons,
    .card-body .button-container,
    .vault-card-bottom-section,
    .reader-card-bottom-section {
        display: flex !important;
        flex-direction: column !important;
        align-items: center !important;
        width: 100% !important;
        margin-top: 15px !important;
        visibility: visible !important;
        opacity: 1 !important;
    }

    /* Override any hidden elements that might contain buttons - Consolidated */
    .card-body .d-none,
    .card-body .hidden,
    .card-body [style*="display: none"],
    .card-body [style*="visibility: hidden"],
    .card-body [style*="opacity: 0"] {
        display: block !important;
        visibility: visible !important;
        opacity: 1 !important;
    }

    /* Author book view specific fixes - Consolidated */
    /* Add space under book covers in mobile views */
    .vault-book .card-header,
    .reader-book .card-header,
    .author-book .card-header,
    .book-card-header,
    .card-header img {
        margin-bottom: 20px !important; /* Add space below the cover */
    }

    /* Ensure proper spacing between image and text content */
    .vault-book-card-column .card-header,
    .reader-book-card-column .card-header,
    .author-book-card-column .card-header {
        margin-bottom: 20px !important;
    }

    /* Additional spacing for single book view */
    .reader-book .card-header img,
    .author-book .card-header img,
    .vault-container .reader-book .card-header img {
        margin-bottom: 20px !important;
    }
}

/* Author book card and book positioning */
.author-book-card,
.author-book {
    position: relative !important;
}

/* Force display of action buttons in author book view - Consolidated */
.author-book-card .btn,
.author-book .btn,
.author-book-card a[href*="amazon"],
.author-book a[href*="amazon"],
.author-book-card a.btn-primary,
.author-book a.btn-primary,
.author-book-card button.btn-primary,
.author-book button.btn-primary,
.author-container .btn-sm.btn-primary {
    display: inline-block !important;
    visibility: visible !important;
    opacity: 1 !important;
    margin: 10px auto !important; /* Centered margin */
    position: static !important;
    width: auto !important; /* Allow width to adjust */
    text-align: center !important; /* Center text */
}

/* Center button container on mobile - Consolidated */
.author-book-card .action-buttons,
.author-book .action-buttons,
.author-book-card .button-container,
.author-book .button-container {
    text-align: center !important;
}

/* Desktop layout for author books */
@media (min-width: 769px) {
    /* Book cover container - align to right */
    .author-book .card-header {
        float: right !important;
        margin-left: 20px !important;
        margin-right: 0 !important;
    }

    /* Text content - align to left */
    .author-book-card h4,
    .author-book-card p,
    .author-book-card .book-summary,
    .author-book-card .book-status,
    .author-book .card-body {
        text-align: left !important;
    }

    /* Clear the row to prevent layout issues */
    .author-book-card:after {
        content: "" !important;
        display: table !important;
        clear: both !important;
    }
}

/* Remove horizontal lines/borders under book covers */
.card-header,
.card-header img,
.author-book .card-header,
.reader-book .card-header,
.vault-book .card-header,
.book-card-header {
    border-bottom: none !important;
    box-shadow: none !important;
    border-radius: 0 !important;
}

/* Remove any horizontal rules */
hr,
.card hr,
.card-body hr,
.author-book hr,
.reader-book hr,
.vault-book hr {
    display: none !important;
}

/* Target the specific horizontal line shown in the image */
.card-header:after,
.card-header::after,
.author-book .card-header:after,
.reader-book .card-header:after,
.vault-book .card-header:after {
    display: none !important;
    content: none !important;
    border: none !important;
}

/* Remove gray background from book covers and their containers */
.card-header,
.book-card-header,
.author-book .card-header,
.reader-book .card-header,
.vault-book .card-header,
.card-header img,
.book-card-header img,
.author-book .card-header img,
.reader-book .card-header img,
.vault-book .card-header img,
.card-header > *,
.book-card-header > * {
    background-color: transparent !important;
    background: none !important;
    box-shadow: none !important;
}

/* Small phones */
@media only screen and (max-width: 480px) {
    html {
        font-size: 20px;
    }
}

/* Tablets and larger phones */
@media only screen and (min-width: 481px) and (max-width: 767px) {
    html {
        font-size: 18px;
    }
}


/* ======================
   RESPONSIVE FONT SIZING
   ====================== */

/* Extra small devices (phones less than 576px) */
@media only screen and (max-width: 575px) {
    html {
        font-size: 19px; /* Largest font size for smallest screens */
    }
     /* Adjust navigation font size */
     .nav-link, .nav-radio-group span {
        font-size: 1.1rem !important;
    }
}

/* Small devices (large phones, 576px and up) */
@media only screen and (min-width: 576px) and (max-width: 767px) {
    html {
        font-size: 18px; /* Slightly smaller for larger phones */
    }

 /* Adjust navigation font size */
 .nav-link, .nav-radio-group span {
    font-size: 1.1rem !important;
}

}

/* Medium devices (tablets, 768px and up) */
@media only screen and (min-width: 768px) and (max-width: 991px) {
    html {
        font-size: 16px; /* Transitional size for tablets */
    }

}

/* Large devices (desktops, 992px and up) */
@media only screen and (min-width: 992px) and (max-width: 1199px) {
    html {
        font-size: 16px; /* Standard size for desktops */
    }
}

/* Extra large devices (large desktops, 1200px and up) */
@media only screen and (min-width: 1200px) {
    html {
        font-size: 16px; /* Maintain standard size for large screens */
    }
}

/* High-DPI mobile devices (for Retina displays and similar) */
@media only screen and (max-width: 767px) and (min-resolution: 2dppx) {
    html {
        font-size: 19px; /* Slightly adjusted for high-resolution small screens */
    }
}

/* Orientation-specific adjustments */
@media only screen and (max-width: 767px) and (orientation: landscape) {
    html {
        font-size: 17px; /* Slightly smaller for landscape mobile */
    }
}

/* Mobile padding for quotes and book content */
@media only screen and (max-width: 767px) {
    /* Top random quotes - all variations */
    .literary-quote,
    .vault-literary-quote,
    .reader-literary-quote,
    .author-literary-quote,
    blockquote {
        padding-left: 20px !important;
        padding-right: 20px !important;
        box-sizing: border-box !important;
    }
    
    /* Book descriptions and excerpts in cards */
    .vault-book .card-body p,
    .reader-book .card-body p,
    .author-book .card-body p,
    .global-book-card p,
    .author-book-card p,
    .book-summary {
        padding-left: 15px !important;
        padding-right: 15px !important;
        box-sizing: border-box !important;
    }
    
    /* Book descriptions in single book view pages */
    .author-book-card .col-md-8 p,
    .book-summary,
    .other-information,
    .favorite-excerpts {
        padding-left: 20px !important;
        padding-right: 20px !important;
        box-sizing: border-box !important;
    }
    
    /* Note cards content (friendly notes) */
    .note-card .note-content,
    .note-card .visible-content {
        padding-left: 15px !important;
        padding-right: 15px !important;
        box-sizing: border-box !important;
    }
    
    /* General container padding for better mobile spacing */
    .page-content-container .container {
        padding-left: 20px !important;
        padding-right: 20px !important;
    }
    
    /* Hero section quotes */
    .hero-section blockquote,
    .author-hero-section blockquote {
        padding-left: 25px !important;
        padding-right: 25px !important;
    }
}

/* Extra small devices (phones less than 576px) - tighter padding */
@media only screen and (max-width: 575px) {
    .literary-quote,
    .vault-literary-quote,
    .reader-literary-quote,
    .author-literary-quote,
    blockquote {
        padding-left: 15px !important;
        padding-right: 15px !important;
    }
    
    .page-content-container .container {
        padding-left: 15px !important;
        padding-right: 15px !important;
    }
}

/* ======================
   FEEDBACK FORM STYLES
   ====================== */
.feedback-flash-message {
    background: none !important;
    background-color: transparent !important;
    border: 1px solid var(--accent-color) !important;
    color: var(--text-color) !important;
    padding: 15px;
    border-radius: 5px;
    margin-bottom: 15px;
    text-align: center;
    font-weight: 300;
    font-size: 0.8rem !important;
}

.feedback-toggle-btn {
    background: none;
    border: none;
    color: var(--accent-color);
    text-decoration: underline;
    cursor: pointer;
    font-size: 14px;
    font-weight: 300;
    padding: 0;
}

.feedback-toggle-btn:hover {
    color: var(--primary-color);
}

.feedback-form-container {
    background: white;
    border: 1px solid #ddd;
    border-radius: 5px;
    padding: 20px;
    margin-top: 15px;
}

.feedback-textarea {
    width: 100%;
    min-height: 100px;
    border: 1px solid #ddd;
    border-radius: 3px;
    padding: 10px;
    font-size: 14px;
    font-weight: 300;
    resize: vertical;
}

.feedback-submit-btn {
    background-color: var(--accent-color) !important;
    border-color: var(--accent-color) !important;
    color: white !important;
    padding: 8px 20px;
    font-size: 14px;
    font-weight: 300;
    border-radius: 3px;
}

.feedback-submit-btn:hover {
    background-color: var(--primary-color) !important;
    border-color: var(--primary-color) !important;
}